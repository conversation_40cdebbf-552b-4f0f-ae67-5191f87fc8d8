:root {
    /* Color Variables */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --info-color: #0891b2;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Font Sizes */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    --font-3xl: 1.875rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* App Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: var(--text-white);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.app-title {
    font-size: var(--font-2xl);
    font-weight: 700;
    margin: 0;
}

.app-subtitle {
    font-size: var(--font-sm);
    margin: var(--spacing-xs) 0 0 0;
    opacity: 0.9;
}

/* Split Layout */
.split-layout {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.left-panel,
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.left-panel {
    border-right: 2px solid var(--border-light);
    background-color: var(--bg-primary);
}

.right-panel {
    background-color: var(--bg-tertiary);
}

/* Panel Headers */
.panel-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background-color: var(--bg-primary);
    flex-shrink: 0;
}

.panel-title {
    font-size: var(--font-xl);
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.panel-subtitle {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
}

/* Left Panel Content */
.left-panel .input-tabs,
.left-panel .tab-content,
.left-panel .controls {
    padding: 0 var(--spacing-lg);
}

.left-panel .tab-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.left-panel .tab-content.active {
    display: flex;
}

.left-panel .input-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tabs */
.input-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: var(--spacing-md);
    flex-shrink: 0;
}

.tab-button {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-base);
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
    flex: 1;
}

.tab-button:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
    flex: 1;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

.input-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-base);
    flex-shrink: 0;
}

.sql-input {
    width: 100%;
    flex: 1;
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: var(--font-sm);
    line-height: 1.5;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    resize: none;
    transition: border-color var(--transition-fast);
    overflow-y: auto;
}

.sql-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.sql-input::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

/* File Upload Container */
.file-upload-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(37, 99, 235, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.15) 100%);
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

.file-upload-content {
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

/* Upload Icon */
.upload-icon-container {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}

.upload-icon {
    width: 32px;
    height: 32px;
    color: var(--text-white);
}

/* Upload Text */
.upload-title {
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.upload-description {
    font-size: var(--font-base);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
}

.upload-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.upload-link:hover {
    color: var(--primary-hover);
}

/* Format Tags */
.upload-formats {
    display: flex;
    gap: var(--spacing-sm);
}

.format-tag {
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.upload-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: var(--font-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-top: var(--spacing-md);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.upload-button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Upload Info */
.upload-info {
    display: flex;
    justify-content: space-around;
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    text-align: center;
}

.info-icon {
    font-size: var(--font-lg);
}

.info-text {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

/* Uploaded Files */
.uploaded-files {
    display: none;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 200px;
    overflow-y: auto;
}

.uploaded-files.show {
    display: flex;
}

.uploaded-files-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: var(--spacing-sm);
}

.files-count {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.clear-all-files {
    background: none;
    border: none;
    color: var(--error-color);
    font-size: var(--font-xs);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.clear-all-files:hover {
    background-color: rgba(220, 38, 38, 0.1);
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.file-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.file-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.file-icon-container {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.file-icon {
    font-size: var(--font-lg);
    color: var(--text-white);
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    color: var(--text-secondary);
    font-size: var(--font-xs);
}

.file-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.file-remove {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    font-size: var(--font-base);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-remove:hover {
    background-color: rgba(220, 38, 38, 0.1);
    transform: scale(1.1);
}

/* Controls */
.controls {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-primary);
    flex-shrink: 0;
}

.scan-button,
.clear-button,
.export-button,
.history-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: var(--font-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.scan-button {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.scan-button:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.clear-button {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
}

.clear-button:hover {
    background-color: var(--border-light);
    color: var(--text-primary);
}

.export-button {
    background-color: var(--success-color);
    color: var(--text-white);
}

.export-button:hover {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.history-button {
    background-color: var(--info-color);
    color: var(--text-white);
}

.history-button:hover {
    background-color: #0e7490;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.button-icon {
    font-size: var(--font-lg);
}

/* Right Panel - Results Section */
.results-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Results Placeholder */
.results-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.placeholder-title {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.placeholder-text {
    font-size: var(--font-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 400px;
    line-height: 1.6;
}

.placeholder-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
}

.feature-item {
    font-size: var(--font-sm);
    color: var(--success-color);
    font-weight: 500;
}

/* Results Content Wrapper */
.results-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: var(--spacing-lg);
    min-height: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-light);
    flex-shrink: 0;
}

.results-title {
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.results-summary {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-sm);
    font-weight: 600;
}

.summary-item.errors {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--error-color);
}

.summary-item.warnings {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.summary-item.info {
    background-color: rgba(8, 145, 178, 0.1);
    color: var(--info-color);
}

.summary-item.success {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

/* Results Content */
.results-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    max-height: calc(100vh - 300px);
    min-height: 500px;
    padding-right: var(--spacing-sm);
}

/* Results Header */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--surface-color), var(--background-color));
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-md);
}

.results-summary h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-lg);
}

.results-summary p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-sm);
}

.toggle-all-btn {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.toggle-all-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.issue-category {
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.category-header:hover {
    background-color: var(--bg-tertiary);
}

.category-header.error {
    background-color: rgba(220, 38, 38, 0.05);
    border-left: 4px solid var(--error-color);
}

.category-header.warning {
    background-color: rgba(217, 119, 6, 0.05);
    border-left: 4px solid var(--warning-color);
}

.category-header.info {
    background-color: rgba(8, 145, 178, 0.05);
    border-left: 4px solid var(--info-color);
}

.category-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: var(--font-lg);
}

.category-icon {
    font-size: var(--font-xl);
}

.category-count {
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-sm);
    font-weight: 600;
}

.category-toggle {
    font-size: var(--font-lg);
    transition: transform var(--transition-fast);
}

.category-toggle.expanded {
    transform: rotate(180deg);
}

.category-content {
    display: none;
    padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.category-content.show {
    display: block;
}

.issue-item {
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-left: 3px solid transparent;
}

.issue-item.error {
    border-left-color: var(--error-color);
}

.issue-item.warning {
    border-left-color: var(--warning-color);
}

.issue-item.info {
    border-left-color: var(--info-color);
}

.issue-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.issue-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-base);
}

.issue-severity {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.issue-severity.error {
    background-color: var(--error-color);
    color: var(--text-white);
}

.issue-severity.warning {
    background-color: var(--warning-color);
    color: var(--text-white);
}

.issue-severity.info {
    background-color: var(--info-color);
    color: var(--text-white);
}

.issue-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.issue-code {
    background-color: var(--bg-dark);
    color: #e2e8f0;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: var(--font-sm);
    overflow-x: auto;
    margin-bottom: var(--spacing-sm);
}

.issue-suggestion {
    background-color: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    color: var(--success-color);
    font-size: var(--font-sm);
}

.issue-suggestion strong {
    color: var(--success-color);
}

/* Loading State */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Issues State */
.no-issues {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.no-issues-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: var(--success-color);
}

.no-issues-title {
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.no-issues-description {
    font-size: var(--font-base);
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .split-layout {
        flex-direction: column;
    }

    .left-panel {
        border-right: none;
        border-bottom: 2px solid var(--border-light);
        max-height: 50vh;
    }

    .right-panel {
        flex: 1;
    }

    .app-title {
        font-size: var(--font-xl);
    }

    .app-subtitle {
        font-size: var(--font-xs);
    }

    .panel-header {
        padding: var(--spacing-md);
    }

    .panel-title {
        font-size: var(--font-lg);
    }

    .controls {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .scan-button,
    .clear-button,
    .export-button {
        justify-content: center;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .results-summary {
        width: 100%;
        justify-content: flex-start;
    }

    .input-tabs {
        margin: 0 var(--spacing-md);
    }

    .tab-button {
        text-align: center;
        border-bottom: 1px solid var(--border-light);
        border-radius: 0;
    }

    .tab-button.active {
        border-bottom-color: var(--primary-color);
    }

    .file-upload-area {
        margin: 0 var(--spacing-md);
        padding: var(--spacing-lg);
        min-height: 150px;
    }

    .upload-info {
        flex-direction: column;
        gap: var(--spacing-sm);
        margin: 0 var(--spacing-md);
    }

    .info-item {
        flex-direction: row;
        justify-content: flex-start;
        text-align: left;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .file-info {
        width: 100%;
    }

    .file-actions {
        align-self: flex-end;
    }

    .upload-title {
        font-size: var(--font-lg);
    }

    .upload-description {
        font-size: var(--font-sm);
    }

    .upload-icon-container {
        width: 48px;
        height: 48px;
    }

    .upload-icon {
        width: 24px;
        height: 24px;
    }

    .placeholder-icon {
        font-size: 3rem;
    }

    .placeholder-title {
        font-size: var(--font-xl);
    }

    .placeholder-text {
        font-size: var(--font-sm);
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 2px solid var(--border-light);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: var(--text-white);
}

.modal-title {
    font-size: var(--font-xl);
    font-weight: 700;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: var(--font-2xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.history-filters {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background-color: var(--bg-tertiary);
}

.filter-select,
.filter-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: var(--font-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.filter-select {
    min-width: 150px;
}

.filter-input {
    flex: 1;
}

.history-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.history-item {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.history-name {
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.history-date {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    background-color: var(--bg-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.history-objective {
    font-size: var(--font-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.history-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.history-detail {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-label {
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.detail-value {
    font-size: var(--font-sm);
    color: var(--text-primary);
}

.no-history {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.no-history-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.no-history-title {
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.no-history-text {
    font-size: var(--font-base);
}