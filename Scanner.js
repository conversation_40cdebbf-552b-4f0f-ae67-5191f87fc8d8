// SQL Standards Scanner
console.log('Scanner.js loaded successfully');

class SQLScanner {
    constructor() {
        this.uploadedFiles = [];
        this.reviewHistory = this.loadReviewHistory();
        this.initializeEventListeners();
        this.rules = this.initializeRules();
    }

    initializeEventListeners() {
        const scanBtn = document.getElementById('scanBtn');
        const clearBtn = document.getElementById('clearBtn');
        const sqlInput = document.getElementById('sqlInput');
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');

        // Main controls
        scanBtn.addEventListener('click', () => this.scanSQL());
        clearBtn.addEventListener('click', () => this.clearAll());

        // Export functionality
        const exportBtn = document.getElementById('exportBtn');
        exportBtn.addEventListener('click', () => this.exportReport());

        // History functionality
        const historyBtn = document.getElementById('historyBtn');
        historyBtn.addEventListener('click', () => this.showReviewHistory());

        // Modal close
        const closeHistoryModal = document.getElementById('closeHistoryModal');
        closeHistoryModal.addEventListener('click', () => this.hideReviewHistory());

        // No auto-resize needed in split layout

        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => this.switchTab(button.dataset.tab));
        });

        // File upload events
        if (fileInput && fileUploadArea) {
            console.log('Setting up file upload events');

            fileInput.addEventListener('change', (e) => {
                console.log('File input change event triggered');
                console.log('Files selected:', e.target.files.length);
                this.handleFileSelect(e);
            });

            // Add click event directly to file input for debugging
            fileInput.addEventListener('click', (e) => {
                console.log('File input clicked directly');
            });

            fileUploadArea.addEventListener('click', (e) => {
                console.log('Upload area clicked');
                // Don't prevent default if clicking on the actual file input
                if (e.target === fileInput) {
                    console.log('Direct file input click');
                    return;
                }
                e.preventDefault();
                e.stopPropagation();
                console.log('Triggering file input click');
                fileInput.click();
            });

            fileUploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            fileUploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            fileUploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));

            console.log('File upload events ready');
        } else {
            console.error('File elements not found:', {
                fileInput: !!fileInput,
                fileUploadArea: !!fileUploadArea
            });
        }
    }

    initializeRules() {
        return {
            // Mandatory Header Standards
            mandatoryHeader: {
                name: 'Mandatory Header Standards',
                icon: '📋',
                rules: [
                    {
                        id: 'missing_header_block',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing mandatory header comment block',
                        suggestion: 'Add header block with Name, Objective, Created by, Created Date, Modified by, Modified Date, and Modification purpose'
                    },
                    {
                        id: 'missing_name',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Name" field in header',
                        suggestion: 'Add "Name: [Object Name]" in the header comment block'
                    },
                    {
                        id: 'missing_objective',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Objective" field in header',
                        suggestion: 'Add "Objective: [Purpose description]" in the header comment block'
                    },
                    {
                        id: 'missing_created_by',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Created by" field in header',
                        suggestion: 'Add "Created by: [Author Name]" in the header comment block'
                    },
                    {
                        id: 'missing_created_date',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Created Date" field in header',
                        suggestion: 'Add "Created Date: DD-MMM-YYYY" in the header comment block'
                    },
                    {
                        id: 'missing_modified_by',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Modified by" field in header',
                        suggestion: 'Add "Modified by: [Modifier Name]" in the header comment block'
                    },
                    {
                        id: 'missing_modified_date',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Modified Date" field in header',
                        suggestion: 'Add "Modified Date: DD-MMM-YYYY" in the header comment block'
                    },
                    {
                        id: 'missing_modification_purpose',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Modification purpose" field in header',
                        suggestion: 'Add "Modification purpose: [Reason for change]" in the header comment block'
                    },
                    {
                        id: 'missing_input_parameters',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Input Parameters" documentation in header',
                        suggestion: 'Add "Input Parameters: [parameter details]" or "Input Parameters: None" in the header comment block'
                    },
                    {
                        id: 'missing_output_parameters',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Output Parameters" documentation in header',
                        suggestion: 'Add "Output Parameters: [output details]" in the header comment block'
                    },
                    {
                        id: 'missing_tables_used',
                        severity: 'error',
                        customCheck: true,
                        message: 'Missing "Tables Used" documentation in header',
                        suggestion: 'Add "Tables Used: [table names]" or "Tables Used: None" in the header comment block'
                    },
                    {
                        id: 'invalid_date_format',
                        severity: 'error',
                        customCheck: true,
                        message: 'Invalid date format in header',
                        suggestion: 'Use DD-MMM-YYYY format (e.g., 15-Jan-2024) for all dates'
                    }
                ]
            },

            // Data Type Standards
            dataTypes: {
                name: 'Data Type Standards',
                icon: '🔤',
                rules: [
                    {
                        id: 'varchar_to_nvarchar',
                        severity: 'warning',
                        pattern: /\bVARCHAR\s*\(\s*\d+\s*\)/gi,
                        message: 'Use NVARCHAR instead of VARCHAR for Unicode support',
                        suggestion: 'Replace VARCHAR with NVARCHAR to support international characters'
                    },
                    {
                        id: 'char_to_nchar',
                        severity: 'warning',
                        pattern: /\bCHAR\s*\(\s*\d+\s*\)/gi,
                        message: 'Use NCHAR instead of CHAR for Unicode support',
                        suggestion: 'Replace CHAR with NCHAR for consistent Unicode handling'
                    },
                    {
                        id: 'text_deprecated',
                        severity: 'error',
                        pattern: /\bTEXT\b/gi,
                        message: 'TEXT data type is deprecated',
                        suggestion: 'Use NVARCHAR(MAX) instead of TEXT for better performance and functionality'
                    },
                    {
                        id: 'ntext_deprecated',
                        severity: 'error',
                        pattern: /\bNTEXT\b/gi,
                        message: 'NTEXT data type is deprecated',
                        suggestion: 'Use NVARCHAR(MAX) instead of NTEXT'
                    }
                ]
            },

            // Naming Conventions
            naming: {
                name: 'Naming Conventions',
                icon: '📝',
                rules: [
                    {
                        id: 'procedure_prefix',
                        severity: 'warning',
                        pattern: /CREATE\s+PROCEDURE\s+(?!sp_|usp_)(\w+)/gi,
                        message: 'Stored procedure should have proper prefix (sp_ or usp_)',
                        suggestion: 'Use sp_ or usp_ prefix for stored procedures'
                    },
                    {
                        id: 'view_prefix',
                        severity: 'warning',
                        pattern: /CREATE\s+VIEW\s+(?!vw_|v_)(\w+)/gi,
                        message: 'View should have proper prefix (vw_ or v_)',
                        suggestion: 'Use vw_ or v_ prefix for views'
                    },
                    {
                        id: 'table_naming',
                        severity: 'info',
                        pattern: /CREATE\s+TABLE\s+(\w*[a-z]\w*)/gi,
                        message: 'Consider using PascalCase for table names',
                        suggestion: 'Use PascalCase naming convention for tables (e.g., UserAccounts)'
                    }
                ]
            },

            // Comments and Documentation
            documentation: {
                name: 'Documentation Standards',
                icon: '📚',
                rules: [
                    {
                        id: 'procedure_comments',
                        severity: 'error',
                        pattern: /CREATE\s+PROCEDURE\s+\w+[\s\S]*?AS\s+BEGIN/gi,
                        message: 'Stored procedure lacks documentation comments',
                        suggestion: 'Add header comments describing purpose, parameters, and return values'
                    },
                    {
                        id: 'view_comments',
                        severity: 'error',
                        pattern: /CREATE\s+VIEW\s+\w+[\s\S]*?AS\s+SELECT/gi,
                        message: 'View lacks documentation comments',
                        suggestion: 'Add comments describing the view purpose and data sources'
                    },
                    {
                        id: 'table_comments',
                        severity: 'error',
                        customCheck: true,
                        message: 'Table lacks documentation comments describing purpose and important columns',
                        suggestion: 'Add comments describing table purpose and important columns before CREATE TABLE statement'
                    }
                ]
            },

            // SQL Best Practices
            bestPractices: {
                name: 'SQL Best Practices',
                icon: '⚡',
                rules: [
                    {
                        id: 'select_star',
                        severity: 'error',
                        pattern: /SELECT\s+\*/gi,
                        message: 'SELECT * is prohibited in production code',
                        suggestion: 'Specify explicit column names for better performance, maintainability, and security'
                    },
                    {
                        id: 'nolock_hint',
                        severity: 'error',
                        pattern: /WITH\s*\(\s*NOLOCK\s*\)/gi,
                        message: 'NOLOCK hint can cause dirty reads',
                        suggestion: 'Consider using READ UNCOMMITTED isolation level or other alternatives'
                    },
                    {
                        id: 'missing_where_update',
                        severity: 'error',
                        customCheck: true,
                        message: 'UPDATE statement without WHERE clause is prohibited',
                        suggestion: 'Always include WHERE clause in UPDATE statements to avoid updating all rows'
                    },
                    {
                        id: 'missing_delete_where',
                        severity: 'error',
                        pattern: /DELETE\s+FROM\s+\w+\s*(?!WHERE)/gi,
                        message: 'DELETE statement without WHERE clause is prohibited',
                        suggestion: 'Always include WHERE clause in DELETE statements to avoid deleting all rows'
                    }
                ]
            },

            // Security Issues
            security: {
                name: 'Security Standards',
                icon: '🔒',
                rules: [
                    {
                        id: 'dynamic_sql',
                        severity: 'warning',
                        pattern: /EXEC\s*\(\s*@\w+/gi,
                        message: 'Dynamic SQL detected - potential SQL injection risk',
                        suggestion: 'Use parameterized queries or sp_executesql with parameters'
                    },
                    {
                        id: 'sql_injection_concat',
                        severity: 'error',
                        pattern: /'\s*\+\s*@\w+\s*\+\s*'/gi,
                        message: 'String concatenation in SQL - SQL injection risk',
                        suggestion: 'Use parameterized queries instead of string concatenation'
                    }
                ]
            },

            // Performance Issues
            performance: {
                name: 'Performance Standards',
                icon: '🚀',
                rules: [
                    {
                        id: 'function_in_where',
                        severity: 'warning',
                        pattern: /WHERE\s+\w*\([^)]*\w+\.[^)]*\)/gi,
                        message: 'Function used on column in WHERE clause',
                        suggestion: 'Avoid functions on columns in WHERE clause for better index usage'
                    },
                    {
                        id: 'leading_wildcard',
                        severity: 'warning',
                        pattern: /LIKE\s+['"]%/gi,
                        message: 'Leading wildcard in LIKE clause',
                        suggestion: 'Leading wildcards prevent index usage - consider full-text search'
                    }
                ]
            }
        };
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    handleFileSelect(event) {
        console.log('File select event triggered');
        const files = Array.from(event.target.files);
        console.log('Files selected:', files.length);
        if (files.length > 0) {
            this.processFiles(files);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.remove('dragover');
    }

    handleFileDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.remove('dragover');
        console.log('Files dropped');
        const files = Array.from(event.dataTransfer.files);
        console.log('Dropped files:', files.length);
        if (files.length > 0) {
            this.processFiles(files);
        }
    }

    processFiles(files) {
        console.log('processFiles called with:', files);

        if (!files || files.length === 0) {
            console.log('No files provided');
            return;
        }

        const validFiles = [];
        const invalidFiles = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            console.log(`Processing: ${file.name}, size: ${file.size}, type: ${file.type}`);

            const extension = file.name.toLowerCase().split('.').pop();
            if (['sql', 'txt'].includes(extension)) {
                validFiles.push(file);
                console.log(`✓ Valid: ${file.name}`);
            } else {
                invalidFiles.push(file);
                console.log(`✗ Invalid: ${file.name} (${extension})`);
            }
        }

        console.log(`Valid: ${validFiles.length}, Invalid: ${invalidFiles.length}`);

        if (validFiles.length === 0) {
            alert('Please select valid SQL files (.sql or .txt)');
            return;
        }

        if (invalidFiles.length > 0) {
            alert(`${invalidFiles.length} file(s) skipped (unsupported format)`);
        }

        let addedCount = 0;
        validFiles.forEach(file => {
            const existingFile = this.uploadedFiles.find(f => f.name === file.name && f.size === file.size);
            if (!existingFile) {
                console.log(`Adding: ${file.name}`);
                this.uploadedFiles.push(file);
                addedCount++;
            } else {
                console.log(`Duplicate: ${file.name}`);
            }
        });

        console.log(`Added: ${addedCount}, Total: ${this.uploadedFiles.length}`);

        if (addedCount > 0) {
            alert(`${addedCount} file(s) added successfully`);
            this.displayUploadedFiles();
        } else if (validFiles.length > 0) {
            alert('All selected files were already uploaded');
        }
    }

    showFileSuccess(message) {
        this.showFileMessage(message, 'success');
    }

    showFileWarning(message) {
        this.showFileMessage(message, 'warning');
    }

    showFileError(message) {
        this.showFileMessage(message, 'error');
    }

    showFileMessage(message, type) {
        // Create a temporary message element
        const messageEl = document.createElement('div');
        messageEl.className = `file-message file-message-${type}`;
        messageEl.textContent = message;

        // Style the message
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 16px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            fontSize: '14px',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease-in-out',
            backgroundColor: type === 'success' ? '#059669' :
                           type === 'warning' ? '#d97706' : '#dc2626'
        });

        document.body.appendChild(messageEl);

        // Animate in
        setTimeout(() => {
            messageEl.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }

    displayUploadedFiles() {
        const uploadedFilesContainer = document.getElementById('uploadedFiles');

        if (this.uploadedFiles.length === 0) {
            uploadedFilesContainer.classList.remove('show');
            return;
        }

        uploadedFilesContainer.classList.add('show');

        const filesHeader = `
            <div class="uploaded-files-header">
                <div class="files-count">📁 ${this.uploadedFiles.length} file${this.uploadedFiles.length !== 1 ? 's' : ''} uploaded</div>
                <button class="clear-all-files" onclick="sqlScanner.clearAllFiles()">Clear All</button>
            </div>
        `;

        const filesContent = this.uploadedFiles.map((file, index) => `
            <div class="file-item">
                <div class="file-info">
                    <div class="file-icon-container">
                        <div class="file-icon">${this.getFileIcon(file.name)}</div>
                    </div>
                    <div class="file-details">
                        <div class="file-name" title="${file.name}">${file.name}</div>
                        <div class="file-size">${this.formatFileSize(file.size)} • ${this.getFileType(file.name)}</div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="file-remove" onclick="sqlScanner.removeFile(${index})" title="Remove file">×</button>
                </div>
            </div>
        `).join('');

        uploadedFilesContainer.innerHTML = filesHeader + filesContent;
    }

    getFileIcon(filename) {
        const extension = filename.toLowerCase().split('.').pop();
        switch (extension) {
            case 'sql': return '🗃️';
            case 'txt': return '📄';
            default: return '📄';
        }
    }

    getFileType(filename) {
        const extension = filename.toLowerCase().split('.').pop();
        return extension.toUpperCase();
    }

    clearAllFiles() {
        this.uploadedFiles = [];
        this.displayUploadedFiles();
        document.getElementById('fileInput').value = '';
    }

    // Debug method to test file content
    async testFileReading() {
        if (this.uploadedFiles.length === 0) {
            console.log('No files uploaded');
            return;
        }

        console.log('Testing file reading...');
        for (let i = 0; i < this.uploadedFiles.length; i++) {
            const file = this.uploadedFiles[i];
            try {
                const content = await this.readFileContent(file);
                console.log(`File ${file.name} content preview:`, content.substring(0, 200) + '...');
            } catch (error) {
                console.error(`Error reading file ${file.name}:`, error);
            }
        }
    }

    removeFile(index) {
        this.uploadedFiles.splice(index, 1);
        this.displayUploadedFiles();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async scanSQL() {
        let sqlCode = '';

        // Get SQL from manual input or files
        const activeTab = document.querySelector('.tab-content.active').id;

        if (activeTab === 'manual-tab') {
            sqlCode = document.getElementById('sqlInput').value.trim();
            if (!sqlCode) {
                this.showFileError('Please enter SQL code to scan');
                return;
            }
        } else if (activeTab === 'file-tab') {
            if (this.uploadedFiles.length === 0) {
                this.showFileError('Please upload SQL files to scan');
                return;
            }

            try {
                this.showFileSuccess('Reading uploaded files...');
                const fileContents = await Promise.all(
                    this.uploadedFiles.map(file => this.readFileContent(file))
                );
                sqlCode = fileContents.join('\n\n-- ===== FILE SEPARATOR ===== --\n\n');

                if (!sqlCode.trim()) {
                    this.showFileError('Uploaded files appear to be empty');
                    return;
                }

                console.log('Files read successfully. Total content length:', sqlCode.length);
                this.showFileSuccess(`Successfully read ${this.uploadedFiles.length} file(s)`);
            } catch (error) {
                console.error('Error reading files:', error);
                this.showFileError('Error reading files: ' + error.message);
                return;
            }
        }

        if (!sqlCode.trim()) {
            this.showFileError('No SQL content found to analyze');
            return;
        }

        this.showLoading();

        // Simulate processing time
        setTimeout(() => {
            const issues = this.analyzeSQL(sqlCode);
            this.displayResults(issues);

            // Add to review history if it has header information
            this.addToHistory(sqlCode, issues);
        }, 500);
    }

    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                const content = e.target.result;
                console.log(`Read file ${file.name}: ${content.length} characters`);
                resolve(content);
            };

            reader.onerror = (e) => {
                console.error(`Error reading file ${file.name}:`, e);
                reject(new Error(`Failed to read file: ${file.name}`));
            };

            reader.onabort = () => {
                reject(new Error(`File reading was aborted: ${file.name}`));
            };

            try {
                reader.readAsText(file, 'UTF-8');
            } catch (error) {
                reject(new Error(`Failed to start reading file: ${file.name}`));
            }
        });
    }

    analyzeSQL(sqlCode) {
        const issues = [];
        const lines = sqlCode.split('\n');

        // Check each rule category
        Object.values(this.rules).forEach(category => {
            category.rules.forEach(rule => {
                if (rule.customCheck) {
                    // Handle custom validation rules
                    const customIssues = this.checkCustomRule(rule, sqlCode, lines);
                    issues.push(...customIssues.map(issue => ({
                        ...issue,
                        category: category.name,
                        categoryIcon: category.icon
                    })));
                } else {
                    // Handle pattern-based rules
                    const matches = [...sqlCode.matchAll(rule.pattern)];

                    matches.forEach(match => {
                        const lineNumber = this.getLineNumber(sqlCode, match.index);
                        const lineContent = lines[lineNumber - 1]?.trim() || '';

                        issues.push({
                            category: category.name,
                            categoryIcon: category.icon,
                            severity: rule.severity,
                            title: rule.message,
                            description: rule.suggestion,
                            line: lineNumber,
                            code: lineContent,
                            ruleId: rule.id
                        });
                    });
                }
            });
        });

        return issues;
    }

    checkCustomRule(rule, sqlCode, lines) {
        const issues = [];

        if (rule.id.startsWith('missing_')) {
            // Check for mandatory header fields
            const headerIssues = this.validateMandatoryHeader(sqlCode, lines);
            return headerIssues.filter(issue => issue.ruleId === rule.id).map(issue => ({
                severity: rule.severity,
                title: rule.message,
                description: rule.suggestion,
                line: issue.line,
                code: issue.code,
                ruleId: rule.id
            }));
        }

        if (rule.id === 'invalid_date_format') {
            return this.validateDateFormat(sqlCode, lines).map(issue => ({
                severity: rule.severity,
                title: rule.message,
                description: rule.suggestion,
                line: issue.line,
                code: issue.code,
                ruleId: rule.id
            }));
        }

        if (rule.id === 'table_comments') {
            return this.validateTableComments(sqlCode, lines).map(issue => ({
                severity: rule.severity,
                title: rule.message,
                description: rule.suggestion,
                line: issue.line,
                code: issue.code,
                ruleId: rule.id
            }));
        }

        if (rule.id === 'missing_where_update') {
            return this.validateUpdateWithoutWhere(sqlCode, lines).map(issue => ({
                severity: rule.severity,
                title: rule.message,
                description: rule.suggestion,
                line: issue.line,
                code: issue.code,
                ruleId: rule.id
            }));
        }

        return issues;
    }

    validateMandatoryHeader(sqlCode, lines) {
        const issues = [];
        const requiredFields = [
            'Name',
            'Objective',
            'Created by',
            'Created Date',
            'Modified by',
            'Modified Date',
            'Modification purpose',
            'Input Parameters',
            'Output Parameters',
            'Tables Used'
        ];

        // Check if there's a comment block at the beginning
        const commentBlockRegex = /\/\*[\s\S]*?\*\//;
        const commentMatch = sqlCode.match(commentBlockRegex);

        if (!commentMatch) {
            issues.push({
                ruleId: 'missing_header_block',
                line: 1,
                code: 'No header comment block found'
            });
            return issues;
        }

        const headerContent = commentMatch[0];
        const headerStartLine = this.getLineNumber(sqlCode, commentMatch.index);

        // Check each required field
        requiredFields.forEach(field => {
            const fieldRegex = new RegExp(`${field}\\s*:`, 'i');
            if (!fieldRegex.test(headerContent)) {
                const ruleId = 'missing_' + field.toLowerCase().replace(/\s+/g, '_');
                issues.push({
                    ruleId: ruleId,
                    line: headerStartLine,
                    code: `Missing "${field}" field in header`
                });
            }
        });

        return issues;
    }

    validateDateFormat(sqlCode, lines) {
        const issues = [];
        const datePattern = /(?:Created Date|Modified Date)\s*:\s*([^\n\r]*)/gi;
        const validDateFormat = /^\d{2}-[A-Za-z]{3}-\d{4}$/;

        let match;
        while ((match = datePattern.exec(sqlCode)) !== null) {
            const dateValue = match[1].trim();
            if (dateValue && !validDateFormat.test(dateValue)) {
                const lineNumber = this.getLineNumber(sqlCode, match.index);
                issues.push({
                    ruleId: 'invalid_date_format',
                    line: lineNumber,
                    code: `Invalid date format: ${dateValue}`
                });
            }
        }

        return issues;
    }

    validateTableComments(sqlCode, lines) {
        const issues = [];
        const createTablePattern = /CREATE\s+TABLE\s+(\w+)/gi;

        let match;
        while ((match = createTablePattern.exec(sqlCode)) !== null) {
            const tableName = match[1];
            const tableStartIndex = match.index;
            const lineNumber = this.getLineNumber(sqlCode, tableStartIndex);

            // Look for comments before the CREATE TABLE statement
            const beforeTableCode = sqlCode.substring(0, tableStartIndex);
            const lastCommentIndex = Math.max(
                beforeTableCode.lastIndexOf('/*'),
                beforeTableCode.lastIndexOf('--')
            );

            let hasValidComment = false;

            if (lastCommentIndex !== -1) {
                // Check if there's a comment within reasonable distance (10 lines before)
                const commentLineNumber = this.getLineNumber(sqlCode, lastCommentIndex);
                const lineDistance = lineNumber - commentLineNumber;

                if (lineDistance <= 10) {
                    // Extract comment content
                    let commentContent = '';
                    if (beforeTableCode.lastIndexOf('/*') > beforeTableCode.lastIndexOf('--')) {
                        // Multi-line comment
                        const commentStart = beforeTableCode.lastIndexOf('/*');
                        const commentEnd = sqlCode.indexOf('*/', commentStart);
                        if (commentEnd !== -1) {
                            commentContent = sqlCode.substring(commentStart, commentEnd + 2);
                        }
                    } else {
                        // Single line comment
                        const commentStart = beforeTableCode.lastIndexOf('--');
                        const lineEnd = sqlCode.indexOf('\n', commentStart);
                        commentContent = sqlCode.substring(commentStart, lineEnd !== -1 ? lineEnd : sqlCode.length);
                    }

                    // Check if comment describes table purpose and columns
                    const hasTablePurpose = /purpose|description|stores|contains|holds|manages/i.test(commentContent);
                    const hasColumnInfo = /column|field|attribute|property/i.test(commentContent);

                    hasValidComment = hasTablePurpose && hasColumnInfo;
                }
            }

            if (!hasValidComment) {
                issues.push({
                    ruleId: 'table_comments',
                    line: lineNumber,
                    code: `CREATE TABLE ${tableName} - Missing descriptive comments`
                });
            }
        }

        return issues;
    }

    validateUpdateWithoutWhere(sqlCode, lines) {
        const issues = [];

        // More precise regex to detect UPDATE without WHERE
        // This handles multi-line UPDATE statements better
        const updateStatements = sqlCode.split(/(?=UPDATE\s+)/gi);

        updateStatements.forEach(statement => {
            if (!statement.trim().startsWith('UPDATE')) return;

            // Check if this UPDATE statement has a WHERE clause
            const hasWhere = /\bWHERE\b/i.test(statement);

            if (!hasWhere) {
                // Find the position of this UPDATE statement in the original code
                const updateIndex = sqlCode.indexOf(statement);
                const lineNumber = this.getLineNumber(sqlCode, updateIndex);

                // Get the first line of the UPDATE statement for display
                const firstLine = statement.split('\n')[0].trim();

                issues.push({
                    ruleId: 'missing_where_update',
                    line: lineNumber,
                    code: firstLine
                });
            }
        });

        return issues;
    }

    getLineNumber(text, index) {
        return text.substring(0, index).split('\n').length;
    }

    displayResults(issues) {
        const resultsPlaceholder = document.getElementById('resultsPlaceholder');
        const resultsContentWrapper = document.getElementById('resultsContentWrapper');
        const resultsSummary = document.getElementById('resultsSummary');
        const resultsContent = document.getElementById('resultsContent');
        const exportBtn = document.getElementById('exportBtn');

        // Hide placeholder and show results
        resultsPlaceholder.style.display = 'none';
        resultsContentWrapper.style.display = 'flex';
        exportBtn.style.display = 'flex'; // Show export button after scan
        document.getElementById('historyBtn').style.display = 'flex'; // Show history button

        // Store issues for export
        this.lastScanResults = {
            issues: issues,
            timestamp: new Date(),
            summary: this.createSummaryData(issues)
        };

        // Create summary
        const totalIssues = issues.length;
        resultsSummary.innerHTML = `
            <h3>📊 Found ${totalIssues} issue${totalIssues !== 1 ? 's' : ''}</h3>
            <p>All categories are expanded below to show all details</p>
        `;

        // Group issues by category
        const groupedIssues = this.groupIssuesByCategory(issues);

        if (issues.length === 0) {
            resultsContent.innerHTML = this.createNoIssuesHTML();
        } else {
            resultsContent.innerHTML = this.createIssuesHTML(groupedIssues);
            this.attachCategoryToggleListeners();
        }
    }

    createSummary(issues) {
        const errorCount = issues.filter(i => i.severity === 'error').length;
        const warningCount = issues.filter(i => i.severity === 'warning').length;
        const infoCount = issues.filter(i => i.severity === 'info').length;

        let summaryHTML = '';
        
        if (errorCount > 0) {
            summaryHTML += `<div class="summary-item errors">❌ ${errorCount} Error${errorCount !== 1 ? 's' : ''}</div>`;
        }
        if (warningCount > 0) {
            summaryHTML += `<div class="summary-item warnings">⚠️ ${warningCount} Warning${warningCount !== 1 ? 's' : ''}</div>`;
        }
        if (infoCount > 0) {
            summaryHTML += `<div class="summary-item info">ℹ️ ${infoCount} Info</div>`;
        }
        if (issues.length === 0) {
            summaryHTML = `<div class="summary-item success">✅ No Issues Found</div>`;
        }

        return summaryHTML;
    }

    groupIssuesByCategory(issues) {
        const grouped = {};
        issues.forEach(issue => {
            if (!grouped[issue.category]) {
                grouped[issue.category] = {
                    icon: issue.categoryIcon,
                    issues: []
                };
            }
            grouped[issue.category].issues.push(issue);
        });
        return grouped;
    }

    createNoIssuesHTML() {
        return `
            <div class="no-issues">
                <div class="no-issues-icon">🎉</div>
                <div class="no-issues-title">Great Job!</div>
                <div class="no-issues-description">No SQL standards violations found in your code.</div>
            </div>
        `;
    }

    createIssuesHTML(groupedIssues) {
        let html = `
            <div class="results-actions">
                <button class="toggle-all-btn" onclick="sqlScanner.toggleAllCategories()">
                    📋 Collapse All
                </button>
            </div>
        `;

        Object.entries(groupedIssues).forEach(([categoryName, categoryData]) => {
            const severityClass = this.getCategorySeverity(categoryData.issues);
            const issueCount = categoryData.issues.length;
            
            html += `
                <div class="issue-category">
                    <div class="category-header ${severityClass}" data-category="${categoryName}">
                        <div class="category-title">
                            <span class="category-icon">${categoryData.icon}</span>
                            <span>${categoryName}</span>
                        </div>
                        <div class="category-count">${issueCount}</div>
                        <div class="category-toggle expanded">▼</div>
                    </div>
                    <div class="category-content show">
                        ${categoryData.issues.map(issue => this.createIssueHTML(issue)).join('')}
                    </div>
                </div>
            `;
        });
        
        return html;
    }

    getCategorySeverity(issues) {
        if (issues.some(i => i.severity === 'error')) return 'error';
        if (issues.some(i => i.severity === 'warning')) return 'warning';
        return 'info';
    }

    createIssueHTML(issue) {
        return `
            <div class="issue-item ${issue.severity}">
                <div class="issue-header">
                    <div class="issue-title">${issue.title}</div>
                    <div class="issue-severity ${issue.severity}">${issue.severity}</div>
                </div>
                <div class="issue-description">${issue.description}</div>
                <div class="issue-code">Line ${issue.line}: ${issue.code}</div>
                <div class="issue-suggestion"><strong>Suggestion:</strong> ${issue.description}</div>
            </div>
        `;
    }

    attachCategoryToggleListeners() {
        document.querySelectorAll('.category-header').forEach(header => {
            header.addEventListener('click', () => {
                const content = header.nextElementSibling;
                const toggle = header.querySelector('.category-toggle');

                content.classList.toggle('show');
                toggle.classList.toggle('expanded');
            });
        });
    }

    toggleAllCategories() {
        const allContents = document.querySelectorAll('.category-content');
        const allToggles = document.querySelectorAll('.category-toggle');
        const toggleBtn = document.querySelector('.toggle-all-btn');

        const isAnyExpanded = Array.from(allContents).some(content => content.classList.contains('show'));

        if (isAnyExpanded) {
            // Collapse all
            allContents.forEach(content => content.classList.remove('show'));
            allToggles.forEach(toggle => toggle.classList.remove('expanded'));
            toggleBtn.innerHTML = '📋 Expand All';
        } else {
            // Expand all
            allContents.forEach(content => content.classList.add('show'));
            allToggles.forEach(toggle => toggle.classList.add('expanded'));
            toggleBtn.innerHTML = '📋 Collapse All';
        }
    }

    showLoading() {
        console.log('Showing loading state...');
        const resultsPlaceholder = document.getElementById('resultsPlaceholder');
        const resultsContentWrapper = document.getElementById('resultsContentWrapper');
        const resultsContent = document.getElementById('resultsContent');

        // Hide placeholder and show results wrapper
        resultsPlaceholder.style.display = 'none';
        resultsContentWrapper.style.display = 'flex';

        resultsContent.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
                Analyzing SQL code...
            </div>
        `;
    }

    createSummaryData(issues) {
        const errorCount = issues.filter(i => i.severity === 'error').length;
        const warningCount = issues.filter(i => i.severity === 'warning').length;
        const infoCount = issues.filter(i => i.severity === 'info').length;

        return {
            total: issues.length,
            errors: errorCount,
            warnings: warningCount,
            info: infoCount
        };
    }

    exportReport() {
        if (!this.lastScanResults) {
            alert('No scan results to export. Please run a scan first.');
            return;
        }

        const reportContent = this.generateReportContent();
        this.downloadReport(reportContent);
    }

    generateReportContent() {
        const { issues, timestamp, summary } = this.lastScanResults;
        const groupedIssues = this.groupIssuesByCategory(issues);

        let report = `SQL STANDARDS SCANNER REPORT
========================================

Generated: ${timestamp.toLocaleString()}
Total Issues: ${summary.total}
- Errors: ${summary.errors}
- Warnings: ${summary.warnings}
- Info: ${summary.info}

========================================

`;

        if (issues.length === 0) {
            report += `✅ EXCELLENT! No SQL standards violations found.

Your code follows all required standards:
- Mandatory header documentation
- Proper data types (NVARCHAR vs VARCHAR)
- No SELECT * usage
- UPDATE statements with WHERE clauses
- Complete table documentation
- Input/Output parameter documentation
- Tables used documentation
`;
        } else {
            Object.entries(groupedIssues).forEach(([categoryName, categoryData]) => {
                report += `\n${categoryData.icon} ${categoryName.toUpperCase()}\n`;
                report += '='.repeat(categoryName.length + 4) + '\n\n';

                categoryData.issues.forEach((issue, index) => {
                    report += `${index + 1}. [${issue.severity.toUpperCase()}] ${issue.title}\n`;
                    report += `   Line ${issue.line}: ${issue.code}\n`;
                    report += `   Suggestion: ${issue.description}\n\n`;
                });
            });
        }

        report += `\n========================================
Report generated by SQL Standards Scanner
For questions or support, contact your development team.
========================================`;

        return report;
    }

    downloadReport(content) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `SQL_Standards_Report_${timestamp}.txt`;

        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    clearAll() {
        // Clear manual input
        document.getElementById('sqlInput').value = '';

        // Clear uploaded files
        this.uploadedFiles = [];
        this.displayUploadedFiles();
        document.getElementById('fileInput').value = '';

        // Show placeholder and hide results
        document.getElementById('resultsPlaceholder').style.display = 'flex';
        document.getElementById('resultsContentWrapper').style.display = 'none';
        document.getElementById('exportBtn').style.display = 'none';
        document.getElementById('historyBtn').style.display = 'none';

        // Clear last scan results
        this.lastScanResults = null;

        // Switch back to manual tab
        this.switchTab('manual');
    }

    // Review History Methods
    loadReviewHistory() {
        const stored = localStorage.getItem('sqlScannerHistory');
        return stored ? JSON.parse(stored) : [];
    }

    saveReviewHistory() {
        localStorage.setItem('sqlScannerHistory', JSON.stringify(this.reviewHistory));
    }

    extractHeaderInfo(sqlCode) {
        const headerRegex = /\/\*[\s\S]*?\*\//;
        const headerMatch = sqlCode.match(headerRegex);

        if (!headerMatch) return null;

        const headerContent = headerMatch[0];
        const extractField = (field) => {
            const regex = new RegExp(`${field}\\s*:\\s*([^\\n\\r]*)`);
            const match = headerContent.match(regex);
            return match ? match[1].trim() : '';
        };

        return {
            name: extractField('Name'),
            objective: extractField('Objective'),
            createdBy: extractField('Created by'),
            createdDate: extractField('Created Date'),
            modifiedBy: extractField('Modified by'),
            modifiedDate: extractField('Modified Date'),
            modificationPurpose: extractField('Modification purpose'),
            inputParameters: extractField('Input Parameters'),
            outputParameters: extractField('Output Parameters'),
            tablesUsed: extractField('Tables Used')
        };
    }

    addToHistory(sqlCode, issues) {
        const headerInfo = this.extractHeaderInfo(sqlCode);
        if (!headerInfo || !headerInfo.name) return;

        const historyItem = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            ...headerInfo,
            issueCount: issues.length,
            errorCount: issues.filter(i => i.severity === 'error').length,
            warningCount: issues.filter(i => i.severity === 'warning').length,
            status: issues.length === 0 ? 'approved' : 'rejected'
        };

        // Remove existing entry with same name if exists
        this.reviewHistory = this.reviewHistory.filter(item => item.name !== headerInfo.name);

        // Add new entry at the beginning
        this.reviewHistory.unshift(historyItem);

        // Keep only last 100 entries
        if (this.reviewHistory.length > 100) {
            this.reviewHistory = this.reviewHistory.slice(0, 100);
        }

        this.saveReviewHistory();
    }

    showReviewHistory() {
        document.getElementById('historyModal').style.display = 'flex';
        this.populateHistoryFilters();
        this.displayHistory();
    }

    hideReviewHistory() {
        document.getElementById('historyModal').style.display = 'none';
    }

    populateHistoryFilters() {
        const purposeFilter = document.getElementById('purposeFilter');
        const authorFilter = document.getElementById('authorFilter');

        // Get unique purposes and authors
        const purposes = [...new Set(this.reviewHistory.map(item => item.modificationPurpose).filter(p => p))];
        const authors = [...new Set(this.reviewHistory.map(item => item.modifiedBy).filter(a => a))];

        // Populate purpose filter
        purposeFilter.innerHTML = '<option value="">All Purposes</option>';
        purposes.forEach(purpose => {
            const option = document.createElement('option');
            option.value = purpose;
            option.textContent = purpose;
            purposeFilter.appendChild(option);
        });

        // Populate author filter
        authorFilter.innerHTML = '<option value="">All Authors</option>';
        authors.forEach(author => {
            const option = document.createElement('option');
            option.value = author;
            option.textContent = author;
            authorFilter.appendChild(option);
        });

        // Add filter event listeners
        purposeFilter.addEventListener('change', () => this.displayHistory());
        authorFilter.addEventListener('change', () => this.displayHistory());
        document.getElementById('searchFilter').addEventListener('input', () => this.displayHistory());
    }

    displayHistory() {
        const historyContent = document.getElementById('historyContent');
        const purposeFilter = document.getElementById('purposeFilter').value;
        const authorFilter = document.getElementById('authorFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

        let filteredHistory = this.reviewHistory;

        // Apply filters
        if (purposeFilter) {
            filteredHistory = filteredHistory.filter(item => item.modificationPurpose === purposeFilter);
        }

        if (authorFilter) {
            filteredHistory = filteredHistory.filter(item => item.modifiedBy === authorFilter);
        }

        if (searchFilter) {
            filteredHistory = filteredHistory.filter(item =>
                item.name.toLowerCase().includes(searchFilter) ||
                item.objective.toLowerCase().includes(searchFilter)
            );
        }

        if (filteredHistory.length === 0) {
            historyContent.innerHTML = `
                <div class="no-history">
                    <div class="no-history-icon">📚</div>
                    <div class="no-history-title">No Review History</div>
                    <div class="no-history-text">No SQL objects have been scanned yet or no items match your filters.</div>
                </div>
            `;
            return;
        }

        historyContent.innerHTML = filteredHistory.map(item => this.createHistoryItemHTML(item)).join('');
    }

    createHistoryItemHTML(item) {
        const statusIcon = item.status === 'approved' ? '✅' : '❌';
        const statusColor = item.status === 'approved' ? 'var(--success-color)' : 'var(--error-color)';

        return `
            <div class="history-item">
                <div class="history-header">
                    <h3 class="history-name">${statusIcon} ${item.name}</h3>
                    <div class="history-date">${new Date(item.timestamp).toLocaleDateString()}</div>
                </div>
                <div class="history-objective">${item.objective}</div>
                <div class="history-details">
                    <div class="history-detail">
                        <div class="detail-label">Created By</div>
                        <div class="detail-value">${item.createdBy}</div>
                    </div>
                    <div class="history-detail">
                        <div class="detail-label">Modified By</div>
                        <div class="detail-value">${item.modifiedBy}</div>
                    </div>
                    <div class="history-detail">
                        <div class="detail-label">Modification Purpose</div>
                        <div class="detail-value">${item.modificationPurpose}</div>
                    </div>
                    <div class="history-detail">
                        <div class="detail-label">Status</div>
                        <div class="detail-value" style="color: ${statusColor}; font-weight: 600;">
                            ${item.status.toUpperCase()} (${item.issueCount} issues)
                        </div>
                    </div>
                    <div class="history-detail">
                        <div class="detail-label">Tables Used</div>
                        <div class="detail-value">${item.tablesUsed || 'Not specified'}</div>
                    </div>
                    <div class="history-detail">
                        <div class="detail-label">Parameters</div>
                        <div class="detail-value">In: ${item.inputParameters || 'None'}<br>Out: ${item.outputParameters || 'None'}</div>
                    </div>
                </div>
            </div>
        `;
    }


}

// Global reference for file removal
let sqlScanner;

// Initialize the scanner when the page loads
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('Initializing SQL Scanner...');
        sqlScanner = new SQLScanner();
        console.log('SQL Scanner initialized successfully');
    } catch (error) {
        console.error('Error initializing SQL Scanner:', error);
        alert('Error loading SQL Scanner. Please check the console for details.');
    }
});

// Also try to initialize if DOMContentLoaded already fired
if (document.readyState === 'loading') {
    console.log('Document still loading, waiting for DOMContentLoaded');
} else {
    console.log('Document already loaded, initializing immediately');
    try {
        sqlScanner = new SQLScanner();
        console.log('SQL Scanner initialized successfully (immediate)');
    } catch (error) {
        console.error('Error initializing SQL Scanner (immediate):', error);
    }
}
