/*
Name: sp_TestProcedure
Objective: Test procedure for SQL scanner validation
Created by: Test User
Created Date: 15-Jan-2024
Modified by: Test User
Modified Date: 15-Jan-2024
Modification purpose: Initial creation
Input Parameters: @UserId INT - User identifier
Output Parameters: UserId, UserName, Email
Tables Used: Users, UserProfiles
*/

CREATE PROCEDURE sp_TestProcedure
    @UserId INT
AS
BEGIN
    SELECT UserId, UserName, Email
    FROM Users u
    INNER JOIN UserProfiles up ON u.UserId = up.UserId
    WHERE u.UserId = @UserId
    AND u.Status = 'Active'
END
